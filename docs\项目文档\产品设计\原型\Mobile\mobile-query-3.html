<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地质灾害风险查询 - 移动端</title>
    <link rel="stylesheet" href="../common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- 天地图API -->
    <script type="text/javascript" src="http://api.tianditu.gov.cn/api?v=4.0&tk=7b0f309efd0864b4ce49fafda0302277"></script>
    <!-- 模拟数据 -->
    <script src="../mock-data-3.js"></script>
    <style>
        /* 移动端特定样式 */
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .phone-container {
            width: 375px; /* 19.5:9比例标准宽度 */
            height: 720px; /* 减小高度，保持比例 */
            background-color: #1a1a1a;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }

        .phone-screen {
            width: 100%;
            height: 100%;
            background-color: white;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .phone-notch {
            position: absolute;
            top: 8px; /* 考虑phone-container的padding */
            left: 50%;
            transform: translateX(-50%);
            width: 150px;
            height: 30px;
            background-color: #1a1a1a;
            border-radius: 0 0 15px 15px;
            z-index: 9999; /* 提高z-index确保在最顶层 */
        }

        .mobile-content-wrapper {
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            /* 移除padding-top，采用更精确的安全区域处理 */
        }

        .mobile-header {
            height: 88px; /* 44px导航栏 + 44px状态栏安全区域 */
            background-color: var(--public-bg-primary);
            border-bottom: 1px solid var(--public-border);
            display: flex;
            align-items: flex-end; /* 内容对齐到底部，避开刘海屏 */
            justify-content: center;
            padding: 0 var(--spacing-md) 12px var(--spacing-md); /* 底部留12px间距 */
            position: relative;
            z-index: 1000;
        }

        .mobile-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--public-primary);
        }

        /* 状态栏模拟 */
        .status-bar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 44px;
            background-color: var(--public-bg-primary);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: var(--public-text-primary);
            z-index: 1002;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .signal-bars {
            display: flex;
            gap: 2px;
            align-items: flex-end;
        }

        .signal-bar {
            width: 3px;
            background-color: var(--public-text-primary);
            border-radius: 1px;
        }

        .signal-bar:nth-child(1) { height: 4px; }
        .signal-bar:nth-child(2) { height: 6px; }
        .signal-bar:nth-child(3) { height: 8px; }
        .signal-bar:nth-child(4) { height: 10px; }

        .mobile-content {
            flex: 1; /* 占用剩余空间 */
            position: relative;
            display: flex;
            flex-direction: column;
        }

        /* 预警横幅样式 */
        .map-warning-banner {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            padding: 8px 12px;
            font-size: 12px;
            font-weight: 500;
            display: none;
            align-items: center;
            gap: 8px;
            box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
            z-index: 1001;
            animation: slideDown 0.3s ease-out;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .map-warning-banner:active {
            transform: scale(0.98);
        }

        .map-warning-banner.show {
            display: flex;
        }

        .map-warning-banner i {
            font-size: 14px;
        }

        /* 警铃晃动动画 - 根据预警等级区分 */
        .warning-bell {
            transform-origin: 50% 0%;
            font-size: 34px !important;
        }

        /* 一级预警 - 最紧急的剧烈摇摆 */
        .warning-bell.level-1 {
            animation: bell-swing-critical 0.5s ease-in-out infinite;
        }

        /* 二级预警 - 紧急摇摆 */
        .warning-bell.level-2 {
            animation: bell-swing-urgent 0.5s ease-in-out infinite;
        }

        /* 三级预警 - 中等摇摆 */
        .warning-bell.level-3 {
            animation: bell-swing-moderate 0.5s ease-in-out infinite;
        }

        /* 四级预警 - 基础摇摆 */
        .warning-bell.level-4 {
            animation: bell-swing 0.5s ease-in-out infinite;
        }

        /* 无预警时 */
        .warning-bell.no-warning {
            animation: none;
        }

        .warning-banner-content {
            flex: 1;
            line-height: 1.3;
        }

        .warning-banner-time {
            font-size: 10px;
            opacity: 0.9;
            margin-top: 2px;
        }

        .warning-banner-hint {
            font-size: 10px;
            opacity: 0.8;
            margin-top: 2px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .warning-banner-hint i {
            font-size: 8px;
            animation: none;
        }

        @keyframes slideDown {
            from {
                transform: translateY(-100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* 一级预警 - 最紧急的剧烈摇摆 */
        @keyframes bell-swing-critical {
            0%, 100% {
                transform: rotate(0deg);
            }
            12.5% {
                transform: rotate(8deg);
            }
            37.5% {
                transform: rotate(-8deg);
            }
            62.5% {
                transform: rotate(6deg);
            }
            87.5% {
                transform: rotate(-6deg);
            }
        }

        /* 二级预警 - 紧急摇摆 */
        @keyframes bell-swing-urgent {
            0%, 100% {
                transform: rotate(0deg);
            }
            25% {
                transform: rotate(6deg);
            }
            75% {
                transform: rotate(-6deg);
            }
        }

        /* 三级预警 - 中等摇摆 */
        @keyframes bell-swing-moderate {
            0%, 100% {
                transform: rotate(0deg);
            }
            33% {
                transform: rotate(4deg);
            }
            67% {
                transform: rotate(-4deg);
            }
        }

        /* 四级预警 - 基础摇摆 */
        @keyframes bell-swing {
            0%, 100% {
                transform: rotate(0deg);
            }
            25% {
                transform: rotate(2deg);
            }
            75% {
                transform: rotate(-2deg);
            }
        }

        .map-container {
            width: 100%;
            flex: 1;
            position: relative;
            background-color: #F8FAFC;
        }

        #mobileMap {
            width: 100%;
            height: 100%;
        }

        /* 底部工具栏 */
        .bottom-toolbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            padding: 8px 12px;
            display: flex;
            justify-content: space-around;
            align-items: center;
            gap: 8px;
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
        }

        /* 定位按钮（在底部工具栏上方） */
        .layer-controls {
            position: absolute;
            bottom: 10px; /* 底部工具栏高度 + 间距 */
            right: var(--spacing-md);
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
            z-index: 1000;
        }

        .layer-btn {
            width: 48px; /* 减小宽度 */
            height: 48px; /* 减小高度 */
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(0, 0, 0, 0.15);
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #374151;
            font-size: 10px; /* 减小文字尺寸 */
            transition: all 0.2s ease;
            backdrop-filter: blur(10px);
            padding: 4px; /* 减小内边距 */
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .layer-btn i {
            font-size: 14px; /* 减小图标尺寸 */
            margin-bottom: 1px; /* 减小间距 */
        }

        .layer-btn span {
            font-weight: 500;
            white-space: nowrap;
            text-align: center;
            line-height: 1;
        }

        .layer-btn:hover {
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .layer-btn.active {
            background: rgba(30, 64, 175, 0.9);
            color: #FFFFFF;
            border-color: #1E40AF;
        }

        .layer-btn.active:hover {
            background: rgba(30, 64, 175, 1);
        }

        /* 底部工具栏按钮样式 */
        .toolbar-btn {
            flex: 1;
            height: 44px;
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #374151;
            font-size: 10px;
            transition: all 0.2s ease;
            padding: 4px;
            min-width: 0;
        }

        .toolbar-btn i {
            font-size: 16px;
            margin-bottom: 2px;
        }

        .toolbar-btn span {
            font-weight: 500;
            white-space: nowrap;
            text-align: center;
            line-height: 1;
        }

        .toolbar-btn:active {
            transform: scale(0.95);
        }

        .toolbar-btn.active {
            background: rgba(30, 64, 175, 0.9);
            color: #FFFFFF;
            border-color: #1E40AF;
        }

        /* 定位按钮特殊样式 */
        .location-btn {
            width: 44px;
            height: 44px;
            background: rgba(59, 130, 246, 0.9);
            border: 1px solid #3B82F6;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: white;
            font-size: 16px;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        .location-btn:active {
            transform: scale(0.95);
        }

        .location-btn:hover {
            background: rgba(59, 130, 246, 1);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }

        /* 移动端图例面板样式 */
        .legend-panel {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px 16px 0 0;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.6);
            transform: translateY(100%);
            transition: transform 0.3s ease;
            z-index: 2000;
            max-height: 60vh;
            overflow: hidden;
        }

        .legend-panel.expanded {
            transform: translateY(0);
        }

        .legend-panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            background-color: rgba(30, 64, 175, 0.05);
        }

        .legend-panel-title {
            font-size: 16px;
            font-weight: 600;
            color: #1E40AF;
            margin: 0;
        }

        .legend-close-btn {
            background: none;
            border: none;
            padding: 8px;
            cursor: pointer;
            color: #6B7280;
            border-radius: 50%;
            transition: all 0.2s ease;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .legend-close-btn:active {
            background-color: rgba(0, 0, 0, 0.1);
            transform: scale(0.95);
        }

        .legend-panel-body {
            padding: 16px;
            max-height: calc(60vh - 80px);
            overflow-y: auto;
        }

        .legend-section {
            margin-bottom: 16px;
        }

        .legend-section:last-child {
            margin-bottom: 0;
        }

        .legend-section-title {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin: 0 0 8px 0;
            padding-bottom: 4px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 13px;
            color: #4B5563;
        }

        .legend-item:last-child {
            margin-bottom: 0;
        }

        /* 图例元素样式 */
        .legend-point {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            margin-right: 8px;
            flex-shrink: 0;
            border: 2px solid #fff;
            box-shadow: 0 0 0 2px currentColor;
        }

        .legend-color {
            width: 16px;
            height: 12px;
            border-radius: 2px;
            margin-right: 8px;
            flex-shrink: 0;
            border: 1px solid rgba(0, 0, 0, 0.2);
        }

        .legend-circle {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-right: 8px;
            flex-shrink: 0;
            border: 2px solid;
        }

        .legend-warning-border {
            width: 16px;
            height: 12px;
            border: 2px solid;
            border-radius: 2px;
            margin-right: 8px;
            flex-shrink: 0;
            animation: warningPulse 2s infinite;
        }

        /* 具体图例颜色 */
        .legend-disaster {
            background-color: #ef4444;
            color: #3B82F6;
        }

        .legend-influence {
            border-color: #ef4444;
            background-color: rgba(239, 68, 68, 0.2);
        }

        .legend-defense-1 {
            background-color: #ef4444;
        }

        .legend-defense-2 {
            background-color: #f59e0b;
        }

        .legend-defense-3 {
            background-color: #CCC1DA;
        }

        .legend-warning-1 {
            border-color: #ff0000;
            background-color: rgba(255, 0, 0, 0.2);
        }

        .legend-warning-2 {
            border-color: #ff6600;
            background-color: rgba(255, 102, 0, 0.2);
        }

        .legend-warning-3 {
            border-color: #ffcc00;
            background-color: rgba(255, 204, 0, 0.2);
        }

        .legend-warning-4 {
            border-color: #0066ff;
            background-color: rgba(0, 102, 255, 0.2);
        }

        @keyframes warningPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* 预警详情面板样式 */
        .warning-detail-panel {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 0 0 12px 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.6);
            border-top: none;
            transform: translateY(-100%);
            transition: transform 0.3s ease;
            z-index: 1002;
            max-height: 60vh;
            overflow: hidden;
            opacity: 0;
            visibility: hidden;
        }

        .warning-detail-panel.expanded {
            transform: translateY(0);
            opacity: 1;
            visibility: visible;
        }

        .warning-detail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 3px 16px 3px 16px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .warning-detail-title {
            font-size: 16px;
            font-weight: 600;
            margin: 0;
        }

        /* 预警等级样式 - 头部 */
        .warning-detail-header.level-1 {
            background-color: rgba(239, 68, 68, 0.05);
        }
        .warning-detail-title.level-1 {
            color: #ef4444;
        }

        .warning-detail-header.level-2 {
            background-color: rgba(249, 115, 22, 0.05);
        }
        .warning-detail-title.level-2 {
            color: #f97316;
        }

        .warning-detail-header.level-3 {
            background-color: rgba(234, 179, 8, 0.05);
        }
        .warning-detail-title.level-3 {
            color: #eab308;
        }

        .warning-detail-header.level-4 {
            background-color: rgba(59, 130, 246, 0.05);
        }
        .warning-detail-title.level-4 {
            color: #3b82f6;
        }

        .warning-detail-close {
            background: none;
            border: none;
            padding: 8px;
            cursor: pointer;
            color: #6B7280;
            border-radius: 50%;
            transition: all 0.2s ease;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .warning-detail-close:active {
            background-color: rgba(0, 0, 0, 0.1);
            transform: scale(0.95);
        }

        .warning-detail-body {
            padding: 16px;
            max-height: calc(70vh - 190px);
            overflow-y: auto;
        }

        .warning-info-item {
            margin-bottom: 12px;
            padding: 12px;
            background-color: rgba(0, 0, 0, 0.02);
            border-radius: 8px;
        }

        /* 预警等级样式 - 信息项 */
        .warning-info-item.level-1 {
            border-left: 4px solid #ef4444;
        }

        .warning-info-item.level-2 {
            border-left: 4px solid #f97316;
        }

        .warning-info-item.level-3 {
            border-left: 4px solid #eab308;
        }

        .warning-info-item.level-4 {
            border-left: 4px solid #3b82f6;
        }

        .warning-info-label {
            font-size: 12px;
            font-weight: 600;
            color: #6B7280;
            margin-bottom: 4px;
        }

        .warning-info-value {
            font-size: 14px;
            color: #374151;
            line-height: 1.4;
        }

        .warning-areas {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 4px;
        }

        .warning-area-tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        /* 预警等级样式 - 区域标签 */
        .warning-area-tag.level-1 {
            background-color: rgba(239, 68, 68, 0.1);
            color: #ef4444;
        }
        .warning-area-tag.level-1:active {
            transform: scale(0.95);
            background-color: rgba(239, 68, 68, 0.2);
        }

        .warning-area-tag.level-2 {
            background-color: rgba(249, 115, 22, 0.1);
            color: #f97316;
        }
        .warning-area-tag.level-2:active {
            transform: scale(0.95);
            background-color: rgba(249, 115, 22, 0.2);
        }

        .warning-area-tag.level-3 {
            background-color: rgba(234, 179, 8, 0.1);
            color: #eab308;
        }
        .warning-area-tag.level-3:active {
            transform: scale(0.95);
            background-color: rgba(234, 179, 8, 0.2);
        }

        .warning-area-tag.level-4 {
            background-color: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }
        .warning-area-tag.level-4:active {
            transform: scale(0.95);
            background-color: rgba(59, 130, 246, 0.2);
        }



        /* 右下角我的位置按钮 */
        .location-control {
            position: absolute;
            bottom: var(--spacing-md); /* 距离地图底部的间距 */
            right: var(--spacing-md);
            z-index: 1000;
        }

        .location-btn {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background-color: var(--public-primary);
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-xl);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 20px;
            color: white;
        }

        .location-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
        }

        .location-btn:active {
            transform: scale(0.95);
        }

        /* 底部浮动按钮 - 我的位置 */
        .location-control {
            position: absolute;
            bottom: 20px;
            right: 16px;
            z-index: 1000;
        }

        .location-btn {
            width: 48px;
            height: 48px;
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(0, 0, 0, 0.15);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--public-primary);
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .location-btn:hover {
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }



        .drawer-handle {
            width: 40px;
            height: 4px;
            background-color: #E5E7EB;
            border-radius: 2px;
            margin: 12px auto 8px;
            cursor: pointer;
        }

        .drawer-header {
            padding: 16px 20px 12px;
            border-bottom: 1px solid #E5E7EB;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .drawer-title {
            font-size: 18px;
            font-weight: 600;
            color: #111827;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .drawer-close {
            width: 32px;
            height: 32px;
            border: none;
            background: none;
            color: #6B7280;
            font-size: 18px;
            cursor: pointer;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .drawer-close:hover {
            background-color: #F3F4F6;
            color: #374151;
        }

        .drawer-content {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        /* 筛选区域 */
        .filter-section {
            padding: 16px 20px;
            border-bottom: 1px solid #E5E7EB;
            background-color: #F9FAFB;
        }

        .filter-row {
            display: flex;
            gap: 12px;
            margin-bottom: 12px;
        }

        .filter-row:last-child {
            margin-bottom: 0;
        }

        .filter-group {
            flex: 1;
        }

        .filter-label {
            display: block;
            font-size: 12px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 4px;
        }

        .filter-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #D1D5DB;
            border-radius: 6px;
            font-size: 14px;
            background-color: white;
            color: #1F2937;
        }

        /* 预警列表区域 */
        .warning-list-section {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .warning-list {
            flex: 1;
            overflow-y: auto;
            padding: 0 20px;
        }

        .warning-item {
            background-color: white;
            border: 1px solid #E5E7EB;
            border-radius: 8px;
            margin: 12px 0;
            padding: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .warning-item:hover {
            border-color: #3B82F6;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
        }

        .warning-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }

        .warning-level {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .warning-level-1 { background-color: #EF4444; }
        .warning-level-2 { background-color: #F59E0B; }
        .warning-level-3 { background-color: #EAB308; }
        .warning-level-4 { background-color: #3B82F6; }

        .warning-title {
            font-size: 14px;
            font-weight: 500;
            color: #111827;
            flex: 1;
            line-height: 1.3;
        }

        .warning-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #6B7280;
            margin-bottom: 8px;
        }

        .warning-towns {
            font-size: 12px;
            color: #6B7280;
            line-height: 1.4;
        }

        .warning-towns-label {
            font-weight: 500;
            color: #374151;
        }

        /* 预警详情展开区域 */
        .warning-detail {
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid #E5E7EB;
            display: none;
        }

        .warning-detail.show {
            display: block;
        }

        .warning-content {
            font-size: 13px;
            line-height: 1.5;
            color: #374151;
            margin-bottom: 12px;
        }

        .town-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .town-tag {
            background-color: #EBF8FF;
            color: #1E40AF;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .town-tag:hover {
            background-color: #1E40AF;
            color: white;
        }

        /* 加载更多指示器 */
        .load-more {
            padding: 16px;
            text-align: center;
            color: #6B7280;
            font-size: 14px;
        }

        /* 响应式调整 */
        @media (max-width: 480px) {
            body {
                padding: 10px;
            }

            .phone-container {
                width: 350px;
                height: 672px; /* 调整后的高度 */
            }
        }

        /* 隐藏天地图版权控件 */
        .tdt-control-copyright {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="phone-notch"></div>
        <div class="phone-screen">
            <div class="mobile-content-wrapper public-theme">
                <!-- 状态栏 -->
                <div class="status-bar">
                    <div class="status-left">
                        <div class="signal-bars">
                            <div class="signal-bar"></div>
                            <div class="signal-bar"></div>
                            <div class="signal-bar"></div>
                            <div class="signal-bar"></div>
                        </div>
                        <span style="margin-left: 6px;">中国移动</span>
                    </div>
                    <div class="status-right">
                        <span>100%</span>
                        <i class="fas fa-battery-full" style="margin-left: 4px;"></i>
                    </div>
                </div>

                <!-- 移动端头部 -->
                <header class="mobile-header">
                    <div class="mobile-title">茂名市地质灾害预警平台</div>
                </header>

                <!-- 主要内容区域 -->
                <div class="mobile-content">
                    <!-- 预警横幅 -->
                    <div class="map-warning-banner" id="mapWarningBanner" style="position: relative;">
                        <i class="fas fa-bell warning-bell"></i>
                        <div class="warning-banner-content">
                            <div id="warningBannerText">当前无预警信息</div>
                            <div class="warning-banner-time" id="warningBannerTime"></div>
                            <div class="warning-banner-hint" id="warningBannerHint">
                                <span>点击查看详情</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>

                        <!-- 预警详情面板 -->
                        <div class="warning-detail-panel" id="warningDetailPanel">
                            <div class="warning-detail-header">
                                <h4 class="warning-detail-title">预警详情</h4>
                                <button class="warning-detail-close" id="warningDetailClose">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="warning-detail-body" id="warningDetailBody">
                                <!-- 预警详情内容将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>

                    <!-- 地图容器 -->
                    <div class="map-container">
                        <div id="mobileMap"></div>

                        <!-- 我的位置按钮（保留在右上角） -->
                        <div class="layer-controls">
                            <button class="location-btn" id="locationBtn" title="我的位置">
                                <i class="fas fa-location-arrow"></i>
                            </button>
                        </div>

                    </div>

                    <!-- 底部工具栏 -->
                    <div class="bottom-toolbar">
                        <button class="toolbar-btn active" id="hazardBtn" title="地质灾害点">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>灾害点</span>
                        </button>
                        <button class="toolbar-btn active" id="riskAreaBtn" title="风险防范区">
                            <i class="fas fa-shield-alt"></i>
                            <span>防范区</span>
                        </button>
                        <button class="toolbar-btn" id="legendBtn" title="图例说明">
                            <i class="fas fa-list-ul"></i>
                            <span>图例</span>
                        </button>
                    </div>
                </div>

                <!-- 图例面板 -->
                <div class="legend-panel" id="legendPanel">
                    <div class="legend-panel-content">
                        <div class="legend-panel-header">
                            <h4 class="legend-panel-title">图例说明</h4>
                            <button class="legend-close-btn" id="legendCloseBtn">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="legend-panel-body">
                            <!-- 地质灾害点 -->
                            <div class="legend-section">
                                <h5 class="legend-section-title">地质灾害点</h5>
                                <div class="legend-item">
                                    <div class="legend-point legend-disaster"></div>
                                    <span>地质灾害点</span>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-circle legend-influence"></div>
                                    <span>地质灾害点影响范围</span>
                                </div>
                            </div>

                            <!-- 风险防范区 -->
                            <div class="legend-section">
                                <h5 class="legend-section-title">风险防范区</h5>
                                <div class="legend-item">
                                    <div class="legend-color legend-defense-1"></div>
                                    <span>一级防范区（高风险）</span>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-color legend-defense-2"></div>
                                    <span>二级防范区（中风险）</span>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-color legend-defense-3"></div>
                                    <span>三级防范区（低风险）</span>
                                </div>
                            </div>

                            <!-- 预警状态 -->
                            <div class="legend-section" id="warningLegendSection" style="display: none;">
                                <h5 class="legend-section-title">预警状态</h5>
                                <div class="legend-item">
                                    <div class="legend-warning-border legend-warning-1"></div>
                                    <span id="warningLegend1Text">一级预警范围内风险区</span>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-warning-border legend-warning-2"></div>
                                    <span id="warningLegend2Text">二级预警范围内风险区</span>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-warning-border legend-warning-3"></div>
                                    <span id="warningLegend3Text">三级预警范围内风险区</span>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-warning-border legend-warning-4"></div>
                                    <span id="warningLegend4Text">四级预警范围内风险区</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


    </div>

    <script>
        // 全局变量
        let map;
        let hazardLayer;
        let riskAreaLayer;
        let userLocation;
        let showHazardPoints = true;
        let showRiskAreas = true;

        // 预警数据（使用mock-data.js中的数据）
        // warningData 将从 mock-data.js 中获取


        // 镇街坐标数据（与public-query.html保持一致）
        var townCoordinates = {
            "合水镇": [110.9456, 22.3567],
            "钱排镇": [110.8234, 22.4123],
            "新宝镇": [110.7890, 22.2890],
            "大成镇": [110.6789, 22.3456],
            "大坡镇": [110.5678, 22.4567],
            "马贵镇": [110.4567, 22.3678],
            "古丁镇": [110.3456, 22.2789],
            "深镇镇": [110.2345, 22.3890],
            "罗坑镇": [110.1234, 22.4901],
            "望夫镇": [110.0123, 22.3012],
            "那霍镇": [110.5890, 22.2123]
        };

        // 区县坐标数据
        var countyCoordinates = {
            "信宜市": [110.9456, 22.3567],
            "高州市": [110.8456, 21.9167],
            "电白区": [111.0123, 21.5123],
            "茂南区": [110.9255, 21.6687],
            "化州市": [110.6378, 21.6628]
        };

        // 区县镇街映射（与public-query.html保持一致）
        var countyTowns = {
            'maonan': ['公馆镇', '袂花镇'],
            'dianbai': ['罗坑镇', '望夫镇', '那霍镇'],
            'gaozhou': ['大坡镇', '马贵镇', '古丁镇', '深镇镇'],
            'huazhou': ['杨梅镇', '平定镇', '中垌镇', '林尘镇'],
            'xinyi': ['合水镇', '钱排镇', '新宝镇', '大成镇']
        };

        // 预警信息管理变量
        var currentPage = 1;
        var pageSize = 10;
        var filteredWarnings = warningData;
        var isLoading = false;

        // 初始化地图
        function initMap() {
            // 创建影像底图URL
            var imageURL = "http://t0.tianditu.gov.cn/img_w/wmts?" +
                "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles" +
                "&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=7b0f309efd0864b4ce49fafda0302277";

            // 创建影像标注URL
            var ciaURL = "http://t0.tianditu.gov.cn/cia_w/wmts?" +
                "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles" +
                "&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=7b0f309efd0864b4ce49fafda0302277";

            // 创建自定义图层对象
            var imgLayer = new T.TileLayer(imageURL, {minZoom: 1, maxZoom: 18});
            var ciaLayer = new T.TileLayer(ciaURL, {minZoom: 1, maxZoom: 18});

            // 初始化地图对象，配置图层
            var config = {layers: [imgLayer, ciaLayer]};
            map = new T.Map("mobileMap", config);

            // 设置地图中心点为茂名市
            map.centerAndZoom(new T.LngLat(110.9255, 21.6687), 11);

            // 初始化数据图层
            initDataLayers();
        }

        // 初始化数据图层
        function initDataLayers() {
            // 创建地质灾害点图层
            hazardLayer = new T.LayerGroup();
            map.addLayer(hazardLayer);

            // 创建风险防范区图层
            riskAreaLayer = new T.LayerGroup();
            map.addLayer(riskAreaLayer);

            // 添加示例数据
            addSampleData();
        }

        // 创建地质灾害点图标（与Web端图例一致：实心红点+白色中圈+蓝色外圈）
        function createDisasterIcon() {
            // 创建SVG图标（移除中文注释避免btoa编码错误）
            var svg = '<svg width="18" height="18" xmlns="http://www.w3.org/2000/svg">' +
                      '<circle cx="9" cy="9" r="8" fill="none" stroke="#3B82F6" stroke-width="2"/>' +
                      '<circle cx="9" cy="9" r="6" fill="none" stroke="#ffffff" stroke-width="2"/>' +
                      '<circle cx="9" cy="9" r="4" fill="#ef4444"/>' +
                      '</svg>';

            // 转换为Data URL
            return 'data:image/svg+xml;base64,' + btoa(svg);
        }

        // 添加示例数据
        function addSampleData() {
            // 添加示例地质灾害点（与P001一致）
            var disasterPoints = [
                {lng: 110.9255, lat: 21.6687, name: "茂名市区地质灾害点1", risk: "高风险", type: "滑坡"},
                {lng: 110.8855, lat: 21.6287, name: "茂南区地质灾害点1", risk: "中风险", type: "泥石流"},
                {lng: 111.0255, lat: 21.7087, name: "电白区地质灾害点1", risk: "低风险", type: "地面塌陷"},
                {lng: 110.8455, lat: 21.5887, name: "高州市地质灾害点1", risk: "高风险", type: "滑坡"},
                {lng: 110.7855, lat: 21.5487, name: "化州市地质灾害点1", risk: "中风险", type: "崩塌"}
            ];

            disasterPoints.forEach(function(point) {
                // 创建自定义图标
                var customIcon = new T.Icon({
                    iconUrl: createDisasterIcon(),
                    iconSize: new T.Point(18, 18),
                    iconAnchor: new T.Point(9, 9)
                });

                // 创建中心点标记
                var marker = new T.Marker(new T.LngLat(point.lng, point.lat), {icon: customIcon});

                // 创建100米影响范围圆圈（统一颜色）
                var circle = new T.Circle(new T.LngLat(point.lng, point.lat), 100, {
                    color: "#3498db",
                    weight: 2,
                    opacity: 0.8,
                    fillColor: "#3498db",
                    fillOpacity: 0.2
                });

                // 创建信息窗口
                var infoWin = new T.InfoWindow();
                infoWin.setContent(`
                    <div style="padding: 12px; min-width: 180px; font-family: Arial, sans-serif;">
                        <h4 style="color: #1e40af; margin: 0 0 8px 0; font-size: 14px;">${point.name}</h4>
                        <p style="margin: 4px 0; font-size: 13px;"><strong>灾害类型:</strong> ${point.type}</p>
                        <p style="margin: 4px 0; font-size: 13px;"><strong>风险等级:</strong> <span style="color: ${getRiskColor(point.risk)}">${point.risk}</span></p>
                        <p style="margin: 4px 0; font-size: 13px;"><strong>影响范围:</strong> 半径100米</p>
                        <p style="margin: 4px 0; font-size: 12px; color: #6b7280;">经度: ${point.lng} | 纬度: ${point.lat}</p>
                    </div>
                `);

                // 添加点击事件
                marker.addEventListener("click", function() {
                    // 计算偏移位置，地图向右向下偏移，避免浮动按钮遮挡信息窗口
                    var offsetLng = point.lng + 0.002; // 地图向右偏移
                    var offsetLat = point.lat + 0.005; // 地图向下偏移（增加幅度）
                    map.centerAndZoom(new T.LngLat(offsetLng, offsetLat), 15);
                    marker.openInfoWindow(infoWin);
                });

                circle.addEventListener("click", function() {
                    // 计算偏移位置，地图向右向下偏移，避免浮动按钮遮挡信息窗口
                    var offsetLng = point.lng + 0.002; // 地图向右偏移
                    var offsetLat = point.lat + 0.005; // 地图向下偏移（增加幅度）
                    map.centerAndZoom(new T.LngLat(offsetLng, offsetLat), 15);
                    marker.openInfoWindow(infoWin);
                });

                // 添加到图层
                hazardLayer.addLayer(marker);
                hazardLayer.addLayer(circle);
            });

            // 使用mock-data.js中的风险防范区数据，转换坐标格式
            var processedRiskAreas = riskAreas.map(function(area) {
                return {
                    points: area.points.map(function(point) {
                        return new T.LngLat(point.lng, point.lat);
                    }),
                    name: area.name,
                    risk: area.risk,
                    area: area.area,
                    population: area.population,
                    towns: area.towns,
                    defenseLevel: area.defenseLevel
                };
            });

            processedRiskAreas.forEach(function(area) {
                // 检查是否在预警范围内
                var isInWarningArea = isAreaInWarning(area);

                var color = getRiskAreaColor(area.risk);
                var polygonStyle;

                if (isInWarningArea.inWarning) {
                    // 使用预警高亮样式
                    polygonStyle = getWarningHighlightStyle(area, isInWarningArea.warningLevel);
                } else {
                    // 使用普通样式
                    polygonStyle = {
                        color: color,
                        weight: 2,
                        opacity: 0.8,
                        fillColor: color,
                        fillOpacity: 0.3
                    };
                }

                var polygon = new T.Polygon(area.points, polygonStyle);

                // 如果在预警范围内，添加到预警区域数组
                if (isInWarningArea.inWarning) {
                    window.warningPolygons.push(polygon);
                }

                // 获取防御措施
                var defenseMeasure = getDefenseMeasure(
                    isInWarningArea.inWarning ? isInWarningArea.warningLevel : null,
                    area.defenseLevel || "二级防范区"
                );

                // 创建信息窗口
                var infoWin = new T.InfoWindow();
                var warningInfo = isInWarningArea.inWarning ?
                    '<p style="color: #ef4444; font-weight: bold;">⚠️ 当前在预警范围内</p>' : '';

                infoWin.setContent(`
                    <div style="padding: 10px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                        <h4 style="margin: 0 0 8px 0; color: #1f2937;">${area.name}</h4>
                        <p style="margin: 4px 0;">风险等级: <span style="color: ${color}; font-weight: bold;">${area.risk}</span></p>
                        <p style="margin: 4px 0;">防范区等级: <span style="color: #1E40AF; font-weight: bold;">${area.defenseLevel || "二级防范区"}</span></p>
                        <p style="margin: 4px 0;">覆盖面积: ${area.area}</p>
                        <p style="margin: 4px 0;">影响人口: ${area.population}</p>
                        ${warningInfo}
                        <div style="
                            margin-top: 12px;
                            padding: 12px;
                            background: linear-gradient(135deg, #fef3c7 0%, #fbbf24 100%);
                            border-radius: 8px;
                            border: 2px solid #f59e0b;
                            box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
                        ">
                            <div style="
                                display: flex;
                                align-items: center;
                                margin-bottom: 6px;
                            ">
                                <span style="
                                    font-size: 16px;
                                    margin-right: 6px;
                                ">🛡️</span>
                                <p style="
                                    margin: 0;
                                    font-weight: bold;
                                    color: #92400e;
                                    font-size: 14px;
                                ">防御措施</p>
                            </div>
                            <p style="
                                margin: 0;
                                color: #92400e;
                                line-height: 1.5;
                                font-weight: 600;
                                font-size: 13px;
                            ">${defenseMeasure}</p>
                        </div>
                    </div>
                `);

                // 添加点击事件
                polygon.addEventListener("click", function() {
                    var center = polygon.getBounds().getCenter();
                    // 计算偏移位置，地图向右向下偏移，避免浮动按钮遮挡信息窗口
                    var offsetLng = center.lng + 0.002; // 地图向右偏移
                    var offsetLat = center.lat + 0.005; // 地图向下偏移（增加幅度）
                    map.centerAndZoom(new T.LngLat(offsetLng, offsetLat), 14);
                    // 打开信息窗口
                    polygon.openInfoWindow(infoWin);
                });

                // 添加到图层
                riskAreaLayer.addLayer(polygon);
            });

            // 如果有预警区域，启动脉冲动画
            if (window.warningPolygons && window.warningPolygons.length > 0) {
                startWarningPulseAnimation();
            }


        }

        // 获取风险等级颜色
        function getRiskColor(risk) {
            switch(risk) {
                case '高风险': return '#e74c3c';
                case '中风险': return '#f39c12';
                case '低风险': return '#27ae60';
                default: return '#6b7280';
            }
        }

        // 获取风险防范区颜色
        function getRiskAreaColor(risk) {
            switch(risk) {
                case '高风险': return '#ef4444';
                case '中风险': return '#f59e0b';
                case '低风险': return '#CCC1DA';
                default: return '#6b7280';
            }
        }



        // 创建用户位置图标（绿色实心圆）
        function createUserLocationIcon() {
            // 创建绿色实心圆SVG图标
            var svg = '<svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">' +
                      '<circle cx="8" cy="8" r="6" fill="#10b981"/>' +
                      '</svg>';

            // 转换为Data URL
            return 'data:image/svg+xml;base64,' + btoa(svg);
        }

        // GPS定位功能 - 定位到茂名市中心（向东移动120米）
        function getCurrentLocation() {
            // 茂名市中心坐标（向东移动120米，约0.0011度）
            const maomingCenter = {
                lng: 110.9366,  // 原坐标110.9255 + 0.0011
                lat: 21.6687
            };

            // 移除之前的用户位置标记
            if (userLocation) {
                map.removeLayer(userLocation);
            }

            // 创建用户位置标记（绿色实心圆）
            userLocation = new T.Marker(new T.LngLat(maomingCenter.lng, maomingCenter.lat), {
                icon: new T.Icon({
                    iconUrl: createUserLocationIcon(),
                    iconSize: new T.Point(16, 16),
                    iconAnchor: new T.Point(8, 8)
                })
            });

            // 添加位置标记到地图
            map.addLayer(userLocation);

            // 定位到茂名市中心，使用合适的缩放级别
            map.centerAndZoom(new T.LngLat(maomingCenter.lng, maomingCenter.lat), 13);

            // 不显示提示信息，静默定位
        }

        // 切换地质灾害点图层
        function toggleHazardLayer() {
            const btn = document.getElementById('hazardBtn');
            showHazardPoints = !showHazardPoints;

            if (showHazardPoints) {
                map.addLayer(hazardLayer);
                btn.classList.add('active');
            } else {
                map.removeLayer(hazardLayer);
                btn.classList.remove('active');
            }
        }

        // 切换风险防范区图层
        function toggleRiskAreaLayer() {
            const btn = document.getElementById('riskAreaBtn');
            showRiskAreas = !showRiskAreas;

            if (showRiskAreas) {
                map.addLayer(riskAreaLayer);
                btn.classList.add('active');
            } else {
                map.removeLayer(riskAreaLayer);
                btn.classList.remove('active');
            }
        }

        // 显示提示信息
        function showToast(message) {
            // 创建提示元素
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background-color: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                z-index: 10000;
                font-size: 14px;
            `;
            toast.textContent = message;

            document.body.appendChild(toast);

            // 2秒后移除
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 2000);
        }

        // 预警信息管理函数
        function filterWarnings() {
            var selectedCounty = document.getElementById('warningCountySelect').value;
            var selectedTown = document.getElementById('warningTownSelect').value;

            // 只有在选择了镇街时才进行筛选
            if (selectedTown !== '') {
                filteredWarnings = warningData.filter(function(warning) {
                    return warning.towns.includes(selectedTown);
                });
            } else if (selectedCounty !== '') {
                filteredWarnings = warningData.filter(function(warning) {
                    var countyTownList = countyTowns[selectedCounty] || [];
                    return warning.towns.some(function(town) {
                        return countyTownList.includes(town);
                    });
                });
            } else {
                // 如果没有选择任何筛选条件，显示所有预警信息
                filteredWarnings = warningData;
            }

            currentPage = 1;
            renderWarningList();
        }

        function updateTownOptions() {
            var selectedCounty = document.getElementById('warningCountySelect').value;
            var townSelect = document.getElementById('warningTownSelect');

            townSelect.innerHTML = '<option value="">全部镇街</option>';

            if (selectedCounty && countyTowns[selectedCounty]) {
                countyTowns[selectedCounty].forEach(function(town) {
                    var option = document.createElement('option');
                    option.value = town;
                    option.textContent = town;
                    townSelect.appendChild(option);
                });
            }

            // 只有在选择了区县时才进行筛选，否则显示所有预警
            if (selectedCounty) {
                filterWarnings();
            } else {
                // 重置为显示所有预警信息
                filteredWarnings = warningData;
                currentPage = 1;
                renderWarningList();
            }
        }

        function renderWarningList() {
            var startIndex = (currentPage - 1) * pageSize;
            var endIndex = startIndex + pageSize;
            var pageWarnings = filteredWarnings.slice(startIndex, endIndex);

            var listContainer = document.getElementById('warningList');

            if (currentPage === 1) {
                listContainer.innerHTML = '';
            }

            pageWarnings.forEach(function(warning) {
                var warningItem = document.createElement('div');
                warningItem.className = 'warning-item';
                warningItem.onclick = function() { toggleWarningDetail(warning, warningItem); };

                // 处理涉及镇街显示，最多显示前3个，超过则显示"等X个镇街"
                var townsDisplay = '';
                if (warning.towns.length <= 3) {
                    townsDisplay = warning.towns.join('、');
                } else {
                    townsDisplay = warning.towns.slice(0, 3).join('、') + '等' + warning.towns.length + '个镇街';
                }

                warningItem.innerHTML =
                    '<div class="warning-header">' +
                        '<div class="warning-level warning-level-' + warning.level + '"></div>' +
                        '<div class="warning-title">' + warning.title + '</div>' +
                    '</div>' +
                    '<div class="warning-meta">' +
                        '<span class="warning-time">' + warning.publishTime + '</span>' +
                        '<span class="warning-areas">' + warning.areas.length + '个区域</span>' +
                    '</div>' +
                    '<div class="warning-towns">' +
                        '<span class="warning-towns-label">涉及：</span>' + townsDisplay +
                    '</div>' +
                    '<div class="warning-detail" id="detail-' + warning.id + '">' +
                        '<div class="warning-content">' + warning.content + '</div>' +
                        '<div class="town-tags" id="towns-' + warning.id + '"></div>' +
                    '</div>';

                listContainer.appendChild(warningItem);

                // 添加镇街标签
                var townTagsContainer = document.getElementById('towns-' + warning.id);
                warning.towns.forEach(function(town) {
                    var townTag = document.createElement('span');
                    townTag.className = 'town-tag';
                    townTag.textContent = town;
                    townTag.onclick = function(e) {
                        e.stopPropagation();
                        locateToTown(town);
                    };
                    townTagsContainer.appendChild(townTag);
                });
            });

            // 检查是否还有更多数据
            var hasMore = endIndex < filteredWarnings.length;
            var loadMoreElement = document.getElementById('loadMore');
            if (hasMore) {
                loadMoreElement.style.display = 'block';
            } else {
                loadMoreElement.style.display = 'none';
            }
        }

        function toggleWarningDetail(warning, itemElement) {
            var detailElement = itemElement.querySelector('.warning-detail');
            var isVisible = detailElement.classList.contains('show');

            // 先隐藏所有其他详情
            document.querySelectorAll('.warning-detail.show').forEach(function(detail) {
                detail.classList.remove('show');
            });

            // 切换当前详情显示状态
            if (!isVisible) {
                detailElement.classList.add('show');
            }
        }

        function locateToTown(townName) {
            if (townCoordinates[townName]) {
                var coords = townCoordinates[townName];
                map.centerAndZoom(new T.LngLat(coords[0], coords[1]), 13);
                closeWarningDetail();
            }
        }

        function locateToCounty(countyName) {
            if (countyCoordinates[countyName]) {
                var coords = countyCoordinates[countyName];
                map.centerAndZoom(new T.LngLat(coords[0], coords[1]), 11);
                closeWarningDetail();
            }
        }

        function loadMoreWarnings() {
            if (isLoading) return;

            var hasMore = (currentPage * pageSize) < filteredWarnings.length;
            if (!hasMore) return;

            isLoading = true;
            var loadMoreElement = document.getElementById('loadMore');
            loadMoreElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 加载中...';

            // 模拟加载延迟
            setTimeout(function() {
                currentPage++;
                renderWarningList();
                isLoading = false;

                var stillHasMore = (currentPage * pageSize) < filteredWarnings.length;
                if (stillHasMore) {
                    loadMoreElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 加载更多...';
                } else {
                    loadMoreElement.style.display = 'none';
                }
            }, 500);
        }



        // 初始化事件绑定
        function initEventListeners() {
            // 定位按钮事件
            document.getElementById('locationBtn').addEventListener('click', getCurrentLocation);

            // 图层控制按钮事件
            document.getElementById('hazardBtn').addEventListener('click', toggleHazardLayer);
            document.getElementById('riskAreaBtn').addEventListener('click', toggleRiskAreaLayer);

            // 抽屉相关的事件绑定已删除


        }

        // 显示预警横幅
        function showActiveWarningBanner() {
            var banner = document.getElementById('mapWarningBanner');
            var bannerText = document.getElementById('warningBannerText');
            var bannerTime = document.getElementById('warningBannerTime');
            var warningBell = banner.querySelector('.warning-bell');

            // 查找生效的预警信息
            var activeWarning = warningData.find(function(warning) {
                return warning.status === '生效';
            });

            if (activeWarning) {
                var levelText = activeWarning.levelDesc + '预警';
                var levelColors = {
                    1: '#ef4444',
                    2: '#f97316',
                    3: '#eab308',
                    4: '#3b82f6'
                };

                bannerText.textContent = levelText + '（' + activeWarning.levelName + '）';
                bannerTime.textContent = '发布时间：' + activeWarning.publishTime;

                // 设置横幅颜色
                var color = levelColors[activeWarning.level] || '#ef4444';
                banner.style.background = 'linear-gradient(135deg, ' + color + ' 0%, ' + color + 'dd 100%)';

                // 设置警铃等级样式
                if (warningBell) {
                    // 清除所有等级类
                    warningBell.classList.remove('level-1', 'level-2', 'level-3', 'level-4', 'no-warning');
                    // 添加当前等级类
                    warningBell.classList.add('level-' + activeWarning.level);
                }

                banner.classList.add('show');

                // 添加点击事件
                banner.onclick = function(e) {
                    e.stopPropagation();
                    var panel = document.getElementById('warningDetailPanel');
                    if (panel.classList.contains('expanded')) {
                        closeWarningDetail();
                    } else {
                        showWarningDetail(activeWarning);
                    }
                };
            } else {
                banner.classList.remove('show');
                // 无预警时设置警铃为无预警状态
                if (warningBell) {
                    warningBell.classList.remove('level-1', 'level-2', 'level-3', 'level-4');
                    warningBell.classList.add('no-warning');
                }
            }
        }

        // 显示预警详情
        function showWarningDetail(warning) {
            var panel = document.getElementById('warningDetailPanel');
            var body = document.getElementById('warningDetailBody');
            var header = panel.querySelector('.warning-detail-header');
            var title = panel.querySelector('.warning-detail-title');

            // 生成预警详情内容
            var levelColors = {
                1: '#ef4444',
                2: '#f97316',
                3: '#eab308',
                4: '#3b82f6'
            };

            // 设置头部样式
            if (header && title) {
                // 清除所有等级类
                header.classList.remove('level-1', 'level-2', 'level-3', 'level-4');
                title.classList.remove('level-1', 'level-2', 'level-3', 'level-4');
                // 添加当前等级类
                header.classList.add('level-' + warning.level);
                title.classList.add('level-' + warning.level);
            }

            var areasHtml = warning.areas.map(function(area) {
                return '<span class="warning-area-tag level-' + warning.level + '" onclick="locateToCounty(\'' + area + '\')">' + area + '</span>';
            }).join('');

            var townsHtml = warning.towns.map(function(town) {
                return '<span class="warning-area-tag level-' + warning.level + '" onclick="locateToTown(\'' + town + '\')">' + town + '</span>';
            }).join('');

            body.innerHTML = `
                <div class="warning-info-item level-${warning.level}">
                    <div class="warning-info-label">预警等级</div>
                    <div class="warning-info-value" style="color: ${levelColors[warning.level]}; font-weight: bold;">
                        ${warning.levelDesc}预警（${warning.levelName}）
                    </div>
                </div>

                <div class="warning-info-item level-${warning.level}">
                    <div class="warning-info-label">预警状态</div>
                    <div class="warning-info-value" style="color: ${warning.status === '生效' ? '#10b981' : '#6b7280'}; font-weight: bold;">
                        ${warning.status}
                    </div>
                </div>

                <div class="warning-info-item level-${warning.level}">
                    <div class="warning-info-label">发布时间</div>
                    <div class="warning-info-value">${warning.publishTime}</div>
                </div>

                <div class="warning-info-item level-${warning.level}">
                    <div class="warning-info-label">影响区域</div>
                    <div class="warning-info-value">
                        <div class="warning-areas">${areasHtml}</div>
                    </div>
                </div>

                <div class="warning-info-item level-${warning.level}">
                    <div class="warning-info-label">涉及镇街</div>
                    <div class="warning-info-value">
                        <div class="warning-areas">${townsHtml}</div>
                    </div>
                </div>

                <div class="warning-info-item level-${warning.level}">
                    <div class="warning-info-label">预警内容</div>
                    <div class="warning-info-value">${warning.content}</div>
                </div>
            `;

            panel.classList.add('expanded');
        }

        // 关闭预警详情
        function closeWarningDetail() {
            var panel = document.getElementById('warningDetailPanel');
            panel.classList.remove('expanded');
        }

        // 初始化预警详情面板
        function initWarningDetailPanel() {
            var closeBtn = document.getElementById('warningDetailClose');
            var panel = document.getElementById('warningDetailPanel');
            var banner = document.getElementById('mapWarningBanner');

            if (closeBtn) {
                closeBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    closeWarningDetail();
                });
            }

            // 阻止面板内部点击事件冒泡
            if (panel) {
                panel.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            }

            // 点击横幅外部关闭详情面板
            document.addEventListener('click', function(e) {
                if (panel && panel.classList.contains('expanded')) {
                    if (!banner.contains(e.target)) {
                        closeWarningDetail();
                    }
                }
            });
        }

        // 初始化图例按钮
        function initLegendButton() {
            var legendBtn = document.getElementById('legendBtn');
            var legendPanel = document.getElementById('legendPanel');
            var legendCloseBtn = document.getElementById('legendCloseBtn');

            if (legendBtn && legendPanel && legendCloseBtn) {
                // 图例按钮点击事件
                legendBtn.addEventListener('click', function() {
                    toggleLegendPanel();
                });

                // 关闭按钮点击事件
                legendCloseBtn.addEventListener('click', function() {
                    closeLegendPanel();
                });

                // 点击面板外部关闭
                document.addEventListener('click', function(e) {
                    if (!legendPanel.contains(e.target) && !legendBtn.contains(e.target)) {
                        closeLegendPanel();
                    }
                });
            }
        }

        // 打开图例面板
        function openLegendPanel() {
            var legendBtn = document.getElementById('legendBtn');
            var legendPanel = document.getElementById('legendPanel');

            legendPanel.classList.add('expanded');
            legendBtn.classList.add('active');

            // 更新预警图例显示
            updateLegendWarningSection();
        }

        // 关闭图例面板
        function closeLegendPanel() {
            var legendBtn = document.getElementById('legendBtn');
            var legendPanel = document.getElementById('legendPanel');

            legendPanel.classList.remove('expanded');
            legendBtn.classList.remove('active');
        }

        // 切换图例面板
        function toggleLegendPanel() {
            var legendPanel = document.getElementById('legendPanel');
            var isExpanded = legendPanel.classList.contains('expanded');

            if (isExpanded) {
                closeLegendPanel();
            } else {
                openLegendPanel();
            }
        }

        // 更新图例中的预警部分
        function updateLegendWarningSection() {
            var warningSection = document.getElementById('warningLegendSection');

            // 查找生效的预警信息
            var activeWarning = warningData.find(function(warning) {
                return warning.status === '生效';
            });

            if (activeWarning) {
                // 显示预警图例部分
                warningSection.style.display = 'block';

                // 更新当前预警等级的文字
                var currentLevelText = document.getElementById('warningLegend' + activeWarning.level + 'Text');
                if (currentLevelText) {
                    currentLevelText.style.fontWeight = 'bold';
                    currentLevelText.style.color = '#1E40AF';
                }
            } else {
                // 隐藏预警图例部分
                warningSection.style.display = 'none';
            }
        }



        // 判断风险防范区是否在预警范围内
        function isAreaInWarning(area) {
            // 查找生效的预警信息
            var activeWarning = warningData.find(function(warning) {
                return warning.status === '生效';
            });

            if (!activeWarning || !area.towns) {
                return { inWarning: false, warningLevel: null };
            }

            // 检查风险防范区的镇街是否在预警涉及的镇街中
            var hasWarningTown = area.towns.some(function(town) {
                return activeWarning.towns.includes(town);
            });

            return {
                inWarning: hasWarningTown,
                warningLevel: activeWarning.level,
                warningTitle: activeWarning.title
            };
        }

        // 获取预警高亮样式
        function getWarningHighlightStyle(area, warningLevel) {
            var baseColor = getRiskAreaColor(area.risk); // 保持原有风险等级颜色
            var warningBorderColor;

            // 根据预警等级设置边框颜色
            switch(warningLevel) {
                case 1:
                    warningBorderColor = '#ff0000'; // 鲜红色边框
                    break;
                case 2:
                    warningBorderColor = '#ff6600'; // 鲜橙色边框
                    break;
                case 3:
                    warningBorderColor = '#ffcc00'; // 鲜黄色边框
                    break;
                case 4:
                    warningBorderColor = '#0066ff'; // 鲜蓝色边框
                    break;
                default:
                    warningBorderColor = '#ff0000';
            }

            return {
                color: warningBorderColor,
                weight: 4, // 适中的边框宽度
                opacity: 1.0,
                fillColor: baseColor, // 保持原有风险等级颜色
                fillOpacity: 0.5 // 稍微提升透明度以突出预警状态
            };
        }

        // 根据预警等级和防范区等级获取防御措施
        function getDefenseMeasure(warningLevel, defenseLevel) {
            // 防御措施映射表
            var defenseMeasures = {
                1: { // 一级预警（红色）
                    "一级防范区": "转移避险；停止户外作业。",
                    "二级防范区": "居家避险并做好转移避险准备；停止户外作业；发现异常及时撤离；加强风险巡排查。",
                    "三级防范区": "居家避险；停止户外作业；加强风险巡排查。"
                },
                2: { // 二级预警（橙色）
                    "一级防范区": "居家避险并做好转移避险准备；发现异常及时撤离；暂停户外作业；加强雨情监测；加强风险巡排查。",
                    "二级防范区": "居家避险；发现异常及时撤离；暂停户外作业；加密雨情监测；加强风险巡排查。",
                    "三级防范区": "居家避险；暂停户外作业；加密雨情监测；加强风险巡排查。"
                },
                3: { // 三级预警（黄色）
                    "一级防范区": "居家避险；发现异常及时撤离；加密雨情监测；加强风险巡排查。",
                    "二级防范区": "居家避险；加密雨情监测；加强风险巡排查。",
                    "三级防范区": "居家避险；密切关注雨情。"
                },
                4: { // 四级预警（蓝色）
                    "一级防范区": "密切关注雨情；加强风险巡排查。",
                    "二级防范区": "密切关注雨情。",
                    "三级防范区": "密切关注雨情。"
                }
            };

            // 如果没有预警，返回基础防范措施
            if (!warningLevel) {
                return "密切关注天气变化，做好日常防范。";
            }

            // 获取对应的防御措施
            var measures = defenseMeasures[warningLevel];
            if (measures && measures[defenseLevel]) {
                return measures[defenseLevel];
            }

            // 默认返回基础措施
            return "密切关注雨情，加强风险巡排查。";
        }

        // 预警脉冲动画相关函数
        window.warningPolygons = [];
        window.warningPulseInterval = null;

        // 启动预警区域脉冲动画
        function startWarningPulseAnimation() {
            if (window.warningPulseInterval) {
                clearInterval(window.warningPulseInterval);
            }

            var pulseState = 0; // 0-1之间的值
            var pulseDirection = 1; // 1为增加，-1为减少

            window.warningPulseInterval = setInterval(function() {
                pulseState += pulseDirection * 0.08; // 更快的变化速度

                if (pulseState >= 1) {
                    pulseState = 1;
                    pulseDirection = -1;
                } else if (pulseState <= 0.3) { // 更大的透明度变化范围
                    pulseState = 0.3;
                    pulseDirection = 1;
                }

                // 更新所有预警区域的透明度
                updateWarningAreasOpacity(pulseState);
            }, 80); // 稍慢的更新频率，更平滑
        }

        // 停止预警区域脉冲动画
        function stopWarningPulseAnimation() {
            if (window.warningPulseInterval) {
                clearInterval(window.warningPulseInterval);
                window.warningPulseInterval = null;
            }
        }

        // 更新预警区域的透明度和边框宽度
        function updateWarningAreasOpacity(opacity) {
            if (!window.warningPolygons) return;

            window.warningPolygons.forEach(function(polygon) {
                if (polygon && polygon.setStyle) {
                    // 计算动态边框宽度（3-5px之间变化，更温和）
                    var dynamicWeight = 3 + (opacity * 2);

                    polygon.setStyle({
                        opacity: opacity,
                        fillOpacity: 0.3 + (opacity * 0.3), // 填充透明度在0.3-0.6之间变化
                        weight: dynamicWeight // 边框宽度温和变化
                    });
                }
            });
        }

        // 页面加载完成后初始化地图
        window.onload = function() {
            initMap();
            initEventListeners();

            // 显示预警横幅
            showActiveWarningBanner();

            // 初始化图例按钮
            initLegendButton();

            // 初始化预警详情面板
            initWarningDetailPanel();
        };
    </script>
</body>
</html>
